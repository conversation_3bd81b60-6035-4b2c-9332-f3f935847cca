const express=require('express');
const router=express.Router();
const upload=require("../config/multer-config");

router.get('/',function(req,res){
    res.send("product router is working");
});
router.post('/create',async function(req,res){
    let {image,name,price,discount,bgcolor,panelcolor,textcolor}=req.body;
    let createdProduct=await productModel.create({
        image,
        name,
        price,
        discount,
        bgcolor,
        panelcolor,
        textcolor,
    });
    productModel.save(createdProduct);
    res.render('createproducts', { success: "Product created successfully" });
});
module.exports=router;
