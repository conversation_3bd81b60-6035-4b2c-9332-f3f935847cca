const express = require('express');
const router = express.Router();
const upload = require("../config/multer-config");
const productModel = require("../models/productmodel");

router.get('/', function (req, res) {
    res.send("product router is working");
});

router.get('/create', function (req, res) {
    res.render('createproducts', { success: "", error: "" });
});

router.post('/create', upload.single('image'), async function (req, res) {
    try {
        let { name, price, discount, bgcolor, panelcolor, textcolor } = req.body;

        let createdProduct = await productModel.create({
            image: req.file ? req.file.buffer : null,
            name,
            price,
            discount,
            bgcolor,
            panelcolor,
            textcolor,
        });

        res.render('createproducts', { success: "Product created successfully" });
    } catch (error) {
        res.render('createproducts', { error: error.message });
    }
});
module.exports = router;
