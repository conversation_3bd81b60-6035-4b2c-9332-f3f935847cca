{"name": "carrycraft", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcrypt": "^6.0.0", "config": "^4.1.1", "connect-flash": "^0.1.1", "cookie-parser": "^1.4.7", "dotenv": "^17.2.2", "express": "^5.1.0", "express-session": "^1.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.0", "multer": "^2.0.2"}}