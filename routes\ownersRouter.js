const express = require('express');
const router = express.Router();
const ownerModel = require("../models/ownersmodel");
const usermodel = require('../models/usermodel');

console.log(process.env.NODE_ENV);
if (process.env.NODE_ENV === "development") {
    router.post('/create', async function (req, res) {
        let owners = await ownerModel.find();
        if (owners.length > 0) {
            return res
                .send(503)
                .send("you don't have permission to create a new owner");
        }
        let { fullname, email, password } = req.body;
        let createdOwner = await ownerModel.create({
            fullname,
            email,
            password,
            products,
        });
        res.status(201).send(createdOwner);
    });
}
router.get('/login', function (req, res) {
    res.render('owner-login');
});
router.get('/signup', function (req, res) {
    res.render('owner-signup');
});

router.post('/login', function (req, res) {
    let user = usermodel.findOne({email: req.body.email});
    if(!user) return res.send("Email or password incorrect");
    if(user.password === req.body.password){
        res.render('shop', {products : []})
    }
    else{
        return res.send('email or password incorrect');
    }
});

router.post('/signup', async function(req, res){
    const {fullname, email, password} = req.body;
    let createdOwner = await ownerModel.create({
        fullname,
        email,
        password,
    });
    res.status(201).send(createdOwner);
})
module.exports = router;
