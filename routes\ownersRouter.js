const express = require('express');
const router = express.Router();
const ownerModel = require("../models/ownersmodel");
const usermodel = require('../models/usermodel');

if (process.env.NODE_ENV === "development") {
    router.post('/create', async function (req, res) {
        try {
            let { fullname, email, password } = req.body;
            let createdOwner = await ownerModel.create({
                fullname,
                email,
                password,
            });
            res.render('createproducts', { success: "Owner created successfully" });
        } catch (error) {
            res.status(500).send(error.message);
        }
    });
}
router.get('/login', function (req, res) {
    res.render('owner-login');
});
router.get('/signup', function (req, res) {
    res.render('owner-signup');
});

router.post('/login', async function (req, res) {
    let user = await usermodel.findOne({ email: req.body.email });
    if (!user) return res.send("Email or password incorrect");
    if (user.password === req.body.password) {
        res.render('shop', { products: [] })
    }
    else {
        return res.send('email or password incorrect');
    }
});

router.post('/signup', async function (req, res) {
    const { fullname, email, password } = req.body;
    let createdOwner = await ownerModel.create({
        fullname,
        email,
        password,
    });
    res.render('createproducts');
})
module.exports = router;
