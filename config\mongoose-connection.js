const mongoose = require('mongoose');
const config = require("config");
const dbgr = require("debug")("development:mongoose");

const mongoURI = config.get("MONGODB_URI");

mongoose.connect(`${mongoURI}/CarryCraft`, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    dbgr("✅ MongoDB connected");
})
.catch(err => {
    console.error("❌ MongoDB connection error:", err);
});

module.exports = mongoose.connection;
