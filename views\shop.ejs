<%- include('./partials/header') %>
    <div class="w-full h-screen flex items-start px-20 py-20">
        <div class="w-[25%] flex h-screen flex-col items-start">
            <div class="flex items-center gap-2">
                <h3>sort by</h3>
                <form action="/shop">
                    <select class="border-[1px] px-2 py-1" name="sortby" id="">
                        <option value="popular">Popular</option>
                        <option value="newest">Newest</option>
                    </select>
                </form>
            </div>
            <div class="flex flex-col mt-20">
                <a class="block w-fit mb-2" href="">New Collection</a>
                <a class="block w-fit mb-2" href="">All Products</a>
                <a class="block w-fit mb-2" href="">Discounted Products</a>
            </div>
            <div class="mt-32">
                <a class="block w-fit mb-2" href="">Filter by :</a>
                <a class="block w-fit mb-2" href="">Availability</a>
                <a class="block w-fit mb-2" href="">Discount</a>
            </div>
        </div>
        <div class="w-[75%] flex flex-col gap-5 h-screen">
            <div class="flex items-start gap-5">
                <% products.forEach(function(product){ %>
                    <div class="w-60">
                        <div class="w-full h-52 flex items-center justify-center bg-[<%= product.bgcolor %>]">
                            <img class="h-[12rem]" src="data:image/jpeg;base64,<%= product.image.toString('base64') %>"
                                alt="">
                        </div>
                        <div
                            class="flex justify-between bg-[<%= product.panelcolor %>] items-center px-4 py-4 text-[<%= product.textcolor %>]">
                            <div>
                                <h3>
                                    <%= product.name %>
                                </h3>
                                <h4>₹ <%= product.price %>
                                </h4>
                            </div>
                            <a class="w-7 h-7 flex items-center justify-center rounded-full bg-white" href="">
                                <i class="ri-add-line"></i>
                            </a>
                        </div>
                    </div>
                    <% }) %>
            </div>
        </div>
    </div>
    <%- include('./partials/footer') %>