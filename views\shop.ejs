<%- include('./partials/header') %>
<div class="w-full min-h-screen flex items-start px-20 py-20">
    <div class="w-[25%] flex flex-col items-start sticky top-20">
        <div class="flex items-center gap-2 mb-6">
            <h3 class="font-semibold">Sort by</h3>
            <form action="/shop" method="get">
                <select class="border-[1px] px-2 py-1 rounded" name="sortby"
                    onchange="this.form.submit()">
                    <option value="popular">Popular</option>
                    <option value="newest">Newest</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                </select>
            </form>
        </div>
        <div class="flex flex-col mb-8">
            <h3 class="font-semibold mb-3">Categories</h3>
            <a
                class="block w-fit mb-2 px-3 py-1 hover:bg-gray-100 rounded transition-colors"
                href="/shop">All Products</a>
            <a
                class="block w-fit mb-2 px-3 py-1 hover:bg-gray-100 rounded transition-colors"
                href="/shop?filter=new">New Collection</a>
            <a
                class="block w-fit mb-2 px-3 py-1 hover:bg-gray-100 rounded transition-colors"
                href="/shop?filter=discount">Discounted Products</a>
        </div>
        <div class="flex flex-col mb-8">
            <h3 class="font-semibold mb-3">Quick Actions</h3>
            <a
                class="block w-fit mb-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                href="/products/create">Add Product</a>
            <a
                class="block w-fit mb-2 px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                href="/owners/login">Admin Panel</a>
            <a
                class="block w-fit mb-2 px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                href="/">Home</a>
        </div>
    </div>
    <div class="w-[75%] flex flex-col gap-5 h-screen">
        <div class="flex items-start gap-5">
            <% products.forEach(function(product){ %>
            <div class="w-60">
                <div
                    class="w-full h-52 flex items-center justify-center bg-[<%= product.bgcolor %>]">
                    <img class="h-[12rem]"
                        src="data:image/jpeg;base64,<%= product.image.toString('base64') %>"
                        alt>
                </div>
                <div
                    class="flex justify-between bg-[<%= product.panelcolor %>] items-center px-4 py-4 text-[<%= product.textcolor %>]">
                    <div>
                        <h3>
                            <%= product.name %>
                        </h3>
                        <h4>₹ <%= product.price %>
                        </h4>
                    </div>
                    <a
                        class="w-7 h-7 flex items-center justify-center rounded-full bg-white"
                        href>
                        <i class="ri-add-line"></i>
                    </a>
                </div>
            </div>
            <% }) %>
        </div>
    </div>
</div>
<%- include('./partials/footer') %>