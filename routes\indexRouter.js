const express=require('express');
const router=express.Router();
const is_loggedin=require("../middlewares/is_loggedin");
const{registerUser,loginUser,logout}=require("../controllers/authcontroller");
router.get("/",function(req,res){
    let error=req.flash("error");
    res.render("index",{error});
});

router.post("/register",registerUser);
router.post("/login",loginUser);
router.post("/logout",logout); 
module.exports=router; 