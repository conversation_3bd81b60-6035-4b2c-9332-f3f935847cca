const express = require('express');
const router = express.Router();
const is_loggedin = require("../middlewares/is_loggedin");
const { registerUser, loginUser, logout } = require("../controllers/authcontroller");
const productModel = require("../models/productmodel");

router.get("/", function (req, res) {
    let error = req.flash("error");
    res.render("index", { error });
});

router.get("/shop", async function (req, res) {
    try {
        let products = await productModel.find();
        res.render("shop", { products });
    } catch (error) {
        res.render("shop", { products: [] });
    }
});

router.post("/logout", logout);
module.exports = router;